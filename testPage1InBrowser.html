<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第一页插画生成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 LIBLIB AI 功能测试</h1>
        <p>这个页面将测试 liblibService.js 的 text2image 和 image2image 功能。</p>

        <div class="status info">
            <strong>测试内容：</strong><br>
            1. Text2Image: 根据文字描述生成第一页插画<br>
            2. Image2Image: 基于参考图片生成新的插画
        </div>

        <div style="margin: 20px 0;">
            <button id="text2imgBtn" onclick="testText2Image()">🎨 测试 Text2Image</button>
            <button id="img2imgBtn" onclick="testImage2Image()" style="margin-left: 10px;">🖼️ 测试 Image2Image</button>
            <button id="bothBtn" onclick="testBothFunctions()" style="margin-left: 10px;">🚀 测试全部功能</button>
        </div>

        <div id="status"></div>
        <div id="log" class="log" style="display: none;"></div>
        <div id="result"></div>
    </div>

    <script type="module">
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        // 初始化liblibService
        let liblibService = null;

        async function initializeService() {
            if (liblibService) return liblibService;

            log('📦 导入liblibService模块');
            const module = await import('./src/services/liblibService.js');
            liblibService = module.default;
            log('✅ 模块导入成功');

            // 初始化API密钥
            log('🔑 初始化API密钥');
            const accessKey = 'VXlp-nUZOKSUC0bMSUqA_w';
            const secretKey = 'vyJel3b6lcZPTmcybbZpQ2jdhvsWXQch';

            liblibService.initializeApiKeys(accessKey, secretKey);
            log('✅ API密钥初始化成功');

            // 检查API状态
            const apiStatus = liblibService.getApiStatus();
            log('🔍 API状态: ' + JSON.stringify(apiStatus, null, 2));

            if (!apiStatus.isInitialized) {
                throw new Error('API密钥初始化失败');
            }

            return liblibService;
        }

        // 测试Text2Image功能
        window.testText2Image = async function() {
            const btn = document.getElementById('text2imgBtn');
            btn.disabled = true;
            btn.textContent = '⏳ Text2Image测试中...';

            try {
                log('🎨 开始测试Text2Image功能');
                showStatus('正在初始化Text2Image测试...', 'info');

                const service = await initializeService();
                showStatus('开始Text2Image生成...', 'info');

                // Text2Image提示词
                const prompt = "a cute brown bear named Bobo sitting in front of a wooden house in the forest, children's illustration style, warm colors";

                log('🔤 Text2Image提示词: ' + prompt);
                log('📏 提示词长度: ' + prompt.length + ' 字符');

                showStatus('正在进行Text2Image生成，预计需要1-2分钟...', 'warning');

                const startTime = Date.now();
                log('⏳ 开始调用Text2Image API');

                const imageUrl = await service.generateImage(prompt, '6-8岁');

                const endTime = Date.now();
                const duration = Math.round((endTime - startTime) / 1000);

                log('🎉 Text2Image生成成功！');
                log('🔗 图片URL: ' + imageUrl);
                log('⏱️ 生成耗时: ' + duration + ' 秒');

                // 显示结果
                showStatus('Text2Image测试成功！', 'success');

                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h3>🎨 Text2Image 测试结果</h3>
                    <p><strong>功能:</strong> Text2Image (文生图)</p>
                    <p><strong>图片URL:</strong> <a href="${imageUrl}" target="_blank">${imageUrl}</a></p>
                    <p><strong>生成耗时:</strong> ${duration} 秒</p>
                    <p><strong>生成时间:</strong> ${new Date().toLocaleString()}</p>
                    <img src="${imageUrl}" alt="Text2Image结果" class="result-image" onload="console.log('Text2Image图片加载成功')" onerror="console.error('Text2Image图片加载失败')">
                `;

                // 保存结果
                const result = {
                    type: 'text2image',
                    imageUrl: imageUrl,
                    prompt: prompt,
                    duration: duration,
                    generatedAt: new Date().toISOString()
                };

                localStorage.setItem('text2image_result', JSON.stringify(result));
                log('💾 Text2Image结果已保存');

            } catch (error) {
                log('❌ Text2Image测试失败: ' + error.message);
                console.error('详细错误:', error);
                showStatus('Text2Image测试失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🎨 测试 Text2Image';
            }
        };

        // 测试Image2Image功能
        window.testImage2Image = async function() {
            const btn = document.getElementById('img2imgBtn');
            btn.disabled = true;
            btn.textContent = '⏳ Image2Image测试中...';

            try {
                log('🖼️ 开始测试Image2Image功能');
                showStatus('正在初始化Image2Image测试...', 'info');

                const service = await initializeService();
                showStatus('开始Image2Image生成...', 'info');

                // 使用默认参考图片（page1.png）
                const referenceImageUrl = service.getDefaultReferenceImageUrl();
                log('🎯 使用参考图片: ' + referenceImageUrl);

                // Image2Image提示词
                const prompt = "a cute brown bear and a white rabbit playing together in the forest, children's illustration style";

                log('🔤 Image2Image提示词: ' + prompt);
                log('📏 提示词长度: ' + prompt.length + ' 字符');

                showStatus('正在进行Image2Image生成，预计需要1-2分钟...', 'warning');

                const startTime = Date.now();
                log('⏳ 开始调用Image2Image API');

                const imageUrl = await service.generateImageFromImage(referenceImageUrl, prompt, '6-8岁');

                const endTime = Date.now();
                const duration = Math.round((endTime - startTime) / 1000);

                log('🎉 Image2Image生成成功！');
                log('🔗 图片URL: ' + imageUrl);
                log('⏱️ 生成耗时: ' + duration + ' 秒');

                // 显示结果
                showStatus('Image2Image测试成功！', 'success');

                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h3>🖼️ Image2Image 测试结果</h3>
                    <p><strong>功能:</strong> Image2Image (图生图)</p>
                    <p><strong>参考图片:</strong> ${referenceImageUrl}</p>
                    <p><strong>生成图片:</strong> <a href="${imageUrl}" target="_blank">${imageUrl}</a></p>
                    <p><strong>生成耗时:</strong> ${duration} 秒</p>
                    <p><strong>生成时间:</strong> ${new Date().toLocaleString()}</p>
                    <img src="${imageUrl}" alt="Image2Image结果" class="result-image" onload="console.log('Image2Image图片加载成功')" onerror="console.error('Image2Image图片加载失败')">
                `;

                // 保存结果
                const result = {
                    type: 'image2image',
                    imageUrl: imageUrl,
                    referenceImageUrl: referenceImageUrl,
                    prompt: prompt,
                    duration: duration,
                    generatedAt: new Date().toISOString()
                };

                localStorage.setItem('image2image_result', JSON.stringify(result));
                log('💾 Image2Image结果已保存');

            } catch (error) {
                log('❌ Image2Image测试失败: ' + error.message);
                console.error('详细错误:', error);
                showStatus('Image2Image测试失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🖼️ 测试 Image2Image';
            }
        };

        // 测试全部功能
        window.testBothFunctions = async function() {
            const btn = document.getElementById('bothBtn');
            btn.disabled = true;
            btn.textContent = '⏳ 全功能测试中...';

            try {
                log('🚀 开始全功能测试');
                showStatus('正在进行全功能测试...', 'info');

                const service = await initializeService();

                // 测试1: Text2Image
                log('📝 第1步: 测试Text2Image功能');
                showStatus('第1步: 测试Text2Image...', 'warning');

                const text2imgPrompt = "a cute brown bear named Bobo sitting in front of a wooden house in the forest, children's illustration style, warm colors";
                const startTime1 = Date.now();
                const text2imgUrl = await service.generateImage(text2imgPrompt, '6-8岁');
                const duration1 = Math.round((Date.now() - startTime1) / 1000);

                log('✅ Text2Image测试完成，耗时: ' + duration1 + ' 秒');

                // 测试2: Image2Image
                log('📝 第2步: 测试Image2Image功能');
                showStatus('第2步: 测试Image2Image...', 'warning');

                const referenceImageUrl = service.getDefaultReferenceImageUrl();
                const img2imgPrompt = "a cute brown bear and a white rabbit playing together in the forest, children's illustration style";
                const startTime2 = Date.now();
                const img2imgUrl = await service.generateImageFromImage(referenceImageUrl, img2imgPrompt, '6-8岁');
                const duration2 = Math.round((Date.now() - startTime2) / 1000);

                log('✅ Image2Image测试完成，耗时: ' + duration2 + ' 秒');

                const totalDuration = duration1 + duration2;

                // 显示综合结果
                showStatus('全功能测试成功完成！', 'success');

                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h3>🚀 全功能测试结果</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4>📊 测试统计</h4>
                        <p><strong>Text2Image:</strong> ✅ 成功 (${duration1}秒)</p>
                        <p><strong>Image2Image:</strong> ✅ 成功 (${duration2}秒)</p>
                        <p><strong>总耗时:</strong> ${totalDuration}秒</p>
                        <p><strong>测试时间:</strong> ${new Date().toLocaleString()}</p>
                    </div>

                    <h4>🎨 Text2Image 结果</h4>
                    <p><strong>提示词:</strong> ${text2imgPrompt}</p>
                    <p><strong>图片URL:</strong> <a href="${text2imgUrl}" target="_blank">${text2imgUrl}</a></p>
                    <img src="${text2imgUrl}" alt="Text2Image结果" class="result-image" style="max-width: 45%; margin-right: 10px;">

                    <h4>🖼️ Image2Image 结果</h4>
                    <p><strong>参考图片:</strong> ${referenceImageUrl}</p>
                    <p><strong>提示词:</strong> ${img2imgPrompt}</p>
                    <p><strong>图片URL:</strong> <a href="${img2imgUrl}" target="_blank">${img2imgUrl}</a></p>
                    <img src="${img2imgUrl}" alt="Image2Image结果" class="result-image" style="max-width: 45%;">
                `;

                // 保存综合结果
                const fullResult = {
                    type: 'full_test',
                    text2image: {
                        imageUrl: text2imgUrl,
                        prompt: text2imgPrompt,
                        duration: duration1
                    },
                    image2image: {
                        imageUrl: img2imgUrl,
                        referenceImageUrl: referenceImageUrl,
                        prompt: img2imgPrompt,
                        duration: duration2
                    },
                    totalDuration: totalDuration,
                    generatedAt: new Date().toISOString()
                };

                localStorage.setItem('full_test_result', JSON.stringify(fullResult));
                log('💾 全功能测试结果已保存');
                log('🎉 全功能测试成功完成！');

            } catch (error) {
                log('❌ 全功能测试失败: ' + error.message);
                console.error('详细错误:', error);
                showStatus('全功能测试失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 测试全部功能';
            }
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第一页插画生成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 第一页插画生成测试</h1>
        <p>这个页面将测试使用LIBLIB AI的text2image功能生成《小熊波波的友谊冒险》第一页插画。</p>
        
        <div class="status info">
            <strong>第一页内容：</strong><br>
            波波是一只住在森林里的小棕熊。他有一双好奇的大眼睛和一颗善良的心。每天早晨，波波都会坐在自己的小木屋前，望着远处的大树和花朵，但他从来不敢走得太远。
        </div>
        
        <button id="generateBtn" onclick="generatePage1Illustration()">🚀 生成第一页插画</button>
        
        <div id="status"></div>
        <div id="log" class="log" style="display: none;"></div>
        <div id="result"></div>
    </div>

    <script type="module">
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        // 生成第一页插画
        window.generatePage1Illustration = async function() {
            const btn = document.getElementById('generateBtn');
            btn.disabled = true;
            btn.textContent = '⏳ 生成中...';
            
            try {
                log('🚀 开始生成第一页插画');
                showStatus('正在初始化...', 'info');
                
                // 动态导入liblibService
                log('📦 导入liblibService模块');
                const { default: liblibService } = await import('./src/services/liblibService.js');
                log('✅ 模块导入成功');
                
                // 初始化API密钥
                log('🔑 初始化API密钥');
                const accessKey = 'VXlp-nUZOKSUC0bMSUqA_w';
                const secretKey = 'vyJel3b6lcZPTmcybbZpQ2jdhvsWXQch';
                
                liblibService.initializeApiKeys(accessKey, secretKey);
                log('✅ API密钥初始化成功');
                
                // 检查API状态
                const apiStatus = liblibService.getApiStatus();
                log('🔍 API状态: ' + JSON.stringify(apiStatus, null, 2));
                
                if (!apiStatus.isInitialized) {
                    throw new Error('API密钥初始化失败');
                }
                
                showStatus('API已初始化，开始生成图片...', 'info');
                
                // 第一页提示词
                const prompt = "a cute brown bear named Bobo with big curious eyes, round face, small black nose, warm brown fur, kind and gentle expression, sitting in front of a small wooden house in the forest, looking at distant trees and flowers, morning sunlight filtering through leaves, children's watercolor illustration style, warm and friendly colors, soft gentle tones, clear outlines, designed for children aged 6-8, cozy and peaceful atmosphere, warm browns and greens, beautiful wildflowers, tall forest trees, peaceful morning scene, slightly shy but curious mood, high quality children's book illustration";
                
                log('🔤 使用提示词: ' + prompt);
                log('📏 提示词长度: ' + prompt.length + ' 字符');
                
                showStatus('正在生成图片，预计需要1-2分钟...', 'warning');
                
                const startTime = Date.now();
                log('⏳ 开始调用LIBLIB AI生成图片');
                
                const imageUrl = await liblibService.generateImage(prompt, '6-8岁');
                
                const endTime = Date.now();
                const duration = Math.round((endTime - startTime) / 1000);
                
                log('🎉 图片生成成功！');
                log('🔗 图片URL: ' + imageUrl);
                log('⏱️ 生成耗时: ' + duration + ' 秒');
                
                // 显示结果
                showStatus('生成成功！', 'success');
                
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h3>🎉 生成结果</h3>
                    <p><strong>图片URL:</strong> <a href="${imageUrl}" target="_blank">${imageUrl}</a></p>
                    <p><strong>生成耗时:</strong> ${duration} 秒</p>
                    <p><strong>生成时间:</strong> ${new Date().toLocaleString()}</p>
                    <img src="${imageUrl}" alt="第一页插画" class="result-image" onload="console.log('图片加载成功')" onerror="console.error('图片加载失败')">
                    
                    <h4>📝 更新代码建议</h4>
                    <p>将以下代码添加到 storyData.ts 的第一页：</p>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">image: "${imageUrl}"</pre>
                `;
                
                // 保存结果到localStorage
                const result = {
                    pageId: 1,
                    imageUrl: imageUrl,
                    prompt: prompt,
                    duration: duration,
                    generatedAt: new Date().toISOString(),
                    service: 'LIBLIB AI Text2Image'
                };
                
                localStorage.setItem('page1_illustration_result', JSON.stringify(result));
                log('💾 结果已保存到浏览器本地存储');
                
            } catch (error) {
                log('❌ 生成失败: ' + error.message);
                console.error('详细错误:', error);
                showStatus('生成失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 生成第一页插画';
            }
        };
    </script>
</body>
</html>
